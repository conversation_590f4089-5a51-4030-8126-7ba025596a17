If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	'Set DefaultEmailAddress for CustomProperty15 if TargetSystem is Universidade DASA (UD)
	'Set DefaultEmailAddress for CustomProperty15 if Employee
	If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
		Value = $FK(UID_Person).DefaultEmailAddress$
	
	'Set Empty Value for CustomProperty15 if External
			
	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\K2\AccountDef") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\K2\TargetSystemName") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then
		
		Value = $FK(UID_Person).DefaultEmailAddress$
		
	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\AccountDefDasa") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).DefaultEmailAddress$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\AccountDefAmericasFunc") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).DefaultEmailAddress$

	Else If $FK(UID_Person).IdentityType$ = "Primary" AndAlso _
		$FK(UID_TSBAccountDef).Ident_TSBAccountDef$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\AccountDefAmericasMed") AndAlso _
		$FK(UID_UNSRootB).Ident_UNSRoot$ = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning") AndAlso _
		$UID_TSBBehavior$ = "TSB-FullManaged" Then

		Value = $FK(UID_Person).DefaultEmailAddress$

	End If
End If